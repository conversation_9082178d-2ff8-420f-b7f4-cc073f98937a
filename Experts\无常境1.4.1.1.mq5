//+------------------------------------------------------------------+
//|                                                        无常境.mq5 |
//|                                                  Optimized Version|
//+------------------------------------------------------------------+
#property copyright "Rz"
#property link      ""
#property version   ""
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Arrays\ArrayLong.mqh>
#include <Generic\HashMap.mqh>

//================================================输入参数==============================================
input double InitialLots = 0.01;       // 初始手数
input int TimeInterval = 300;          // 开单间隔(秒)
input int TakeProfit = 0;              // 止盈点数(0=禁用)
input int StopLoss = 400;              // 止损点数(0=禁用)
input int BreakevenPoints = 400;       // 保本点数
input int AdditionalPoints = 10;       // 附加盈利点数
input string StartTime = "00:00";      // 交易开始时间
input string EndTime = "23:59";        // 交易结束时间
input int CheckOrdersCount = 5;        // 订单数量达到N个后检查保本，0=禁用检查
input int MagicNumber = 16878;         // 魔术号
input string OrderComment = "";        // 订单备注

//================================================数据结构==============================================
struct SymbolCache {
    double point;
    int digits;
    double minLots;
    double maxLots;
    double lotStep;
    int lotDigits;
};  // 品种参数缓存

struct TimeCache {
    int startMinutes;
    int endMinutes;
};  // 时间参数缓存

//================================================全局变量==============================================
SymbolCache symCache;                 // 品种缓存实例
TimeCache timeCache;                  // 时间缓存实例
CTrade trade;                         // 交易对象
CPositionInfo m_position;             // 持仓信息对象
CArrayLong m_arr_tickets;             // 订单号数组
ulong breakEvenMap[];                 // 保本订单记录
ulong processedTickets[];             // 已处理订单数组
datetime lastOrderTime = 0;           // 最后开仓时间
string orderComment;                  // 订单注释
int allowedNewPositions = 0;          // 允许开的新仓数量
double lastClosingBalance = 0.0;      // 上次平仓结余

// 手数格式化缓存变量
double g_minVolume = 0.0;
double g_maxVolume = 0.0;
double g_stepVolume = 0.0;
int g_volumeDigits = 2;

// 订单重试映射
CHashMap<ulong, int> orderRetryMap; // 键=票号(ticket)，值=重试次数(retryCount)
CHashMap<ulong, bool> isModifiedMap; // 记录订单是否已修改过保本止损

// 保本状态标志
bool breakeven_activated = false; // 保本状态标志

// 极简版盈亏统计 - 移除复杂缓存机制

//================================================UI控件定义============================================
#define CLOSE_BUTTON "CloseAllButton"       // 平仓按钮名称
#define LABEL_LEVERAGE "LeverageLabel"       // 杠杆标签
#define LABEL_MARGIN_LEVEL "MarginLevelLabel" // 预付款维持率标签
#define LABEL_BALANCE "BalanceLabel"        // 余额标签
#define LABEL_EQUITY "EquityLabel"          // 净值标签
#define LABEL_BUY_VOLUME "BuyVolumeLabel"    // 多单手数标签
#define LABEL_SELL_VOLUME "SellVolumeLabel"  // 空单手数标签
#define LABEL_HISTORY_PL "HistoryPLLabel"   // 历史盈亏标签
#define LABEL_TOTAL_PL "TotalPLLabel"       // 总盈亏标签
#define INFO_PANEL_BG "InfoPanel_BG"        // 信息面板背景
#define HLINE_BUTTON "HLineButton"          // 水平线按钮名称
#define HLINE_TRADE_BUTTON "HLineTradeButton" // 水平线开仓按钮名称
#define RANDOM_TRADE_BUTTON "RandomTradeButton" // 随机开仓按钮名称
#define RZ_LINE "Rz_line"                  // 水平线名称
#define BALANCE_EDIT "BalanceEdit"         // 上次平仓结余编辑框

bool isHLineTradeActive = false;        // 水平线开仓按钮状态
bool isRandomTradeActive = false;       // 随机开仓按钮状态

//================================================常量定义==============================================
#define RTOTAL 3                        // 平仓重试次数
#define SLEEPTIME 1000                  // 重试间隔(毫秒)

// 日志级别定义
#define LOG_LEVEL_ERROR   0             // 只记录错误
#define LOG_LEVEL_WARNING 1             // 记录错误和警告
#define LOG_LEVEL_INFO    2             // 记录错误、警告和一般信息
#define LOG_LEVEL_DEBUG   3             // 记录所有信息，包括调试信息

// 当前日志级别设置
int currentLogLevel = LOG_LEVEL_INFO;  // 默认记录错误、警告和一般信息

//+------------------------------------------------------------------+
//| 统一日志输出函数                                                 |
//+------------------------------------------------------------------+
void LogMessage(int level, string message)
{
    // 只有当消息级别小于或等于当前日志级别时才输出
    if(level <= currentLogLevel)
    {
        string levelText = "";
        color messageColor = clrWhite;

        switch(level)
        {
            case LOG_LEVEL_ERROR:
                levelText = "[错误] ";
                messageColor = clrRed;
                break;
            case LOG_LEVEL_WARNING:
                levelText = "[警告] ";
                messageColor = clrYellow;
                break;
            case LOG_LEVEL_INFO:
                levelText = "[信息] ";
                messageColor = clrWhite;
                break;
            case LOG_LEVEL_DEBUG:
                levelText = "[调试] ";
                messageColor = clrGray;
                break;
        }

        // 添加时间戳
        string timestamp = TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + " ";

        // 输出到日志
        Print(timestamp + levelText + message);
    }
}

//================================================事件处理函数============================================
//+------------------------------------------------------------------+
//| EA初始化函数                                                    |
//+------------------------------------------------------------------+
// 【简化】交易前验证机制 - 基于12.mq5思路
bool ValidateBeforeOrder(ENUM_ORDER_TYPE order_type, double sl, double tp) {
   // 基本检查：确保交易允许
   if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE)) {
      LogMessage(LOG_LEVEL_WARNING, "当前品种不允许交易");
      return false;
   }

   // 简化验证：只检查基本的止损止盈设置
   if(sl > 0 || tp > 0) {
      int stopsLevel = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
      if(stopsLevel > 0) {
         // 基本的安全距离检查（开仓时通常不会有问题）
      }
   }

   return true;
}

// 检查并在满足条件时应用保本
bool CheckBreakeven(ulong ticket) {
    if (!PositionSelectByTicket(ticket)) return false;

    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);

    MqlTick lastTick;
    if (!SymbolInfoTick(_Symbol, lastTick)) {
        LogMessage(LOG_LEVEL_ERROR, "获取最新报价失败");
        return false;
    }

    // 更精确的价格计算（考虑买卖差）
    double currentPrice = (posType == POSITION_TYPE_BUY)
        ? lastTick.bid + (lastTick.ask - lastTick.bid) * 0.1
        : lastTick.ask - (lastTick.ask - lastTick.bid) * 0.1;

    double breakeven_activation_points = BreakevenPoints * _Point;
    bool reachedBreakeven = false;

    if (posType == POSITION_TYPE_BUY) {
        reachedBreakeven = (currentPrice - openPrice >= breakeven_activation_points);
    } else {
        reachedBreakeven = (openPrice - currentPrice >= breakeven_activation_points);
    }



    if (reachedBreakeven && !ArrayContains(breakEvenMap, ticket)) {
        // 【智能保本】计算目标止损 - 预先验证并调整
        double targetSL = (posType == POSITION_TYPE_BUY)
            ? openPrice + AdditionalPoints * _Point
            : openPrice - AdditionalPoints * _Point;

        // 【关键】预验证机制 - 严格按用户设置执行
        double strictSL = CalculateStrictBreakeven(posType, openPrice, AdditionalPoints);

        // 预验证是否可以执行（基于Amazing.mq5和ClosePositionsModule的做法）
        if (!CanExecuteBreakeven(strictSL, posType, currentPrice, currentSL)) {
            return false;
        }

        if (MathAbs(strictSL - currentSL) < _Point / 2) {
            ArrayAppend(breakEvenMap, ticket);
            //LogMessage(LOG_LEVEL_INFO, StringFormat("止损已符合要求，仅标记为已处理 (票号: %d)", ticket));
            return true;
        }

        // 🚀 使用预验证通过的止损价格，确保立即执行成功
        if (trade.PositionModify(ticket, strictSL, PositionGetDouble(POSITION_TP))) {
            ArrayAppend(breakEvenMap, ticket);
            LogMessage(LOG_LEVEL_INFO, StringFormat("✅ 【保本成功】票号: %d, 止损: %.5f (开仓价%s%d点)",
                ticket, strictSL, (posType == POSITION_TYPE_BUY ? "+" : "-"), AdditionalPoints));
            return true;
        } else {
            int errorCode = GetLastError();
            LogMessage(LOG_LEVEL_WARNING, StringFormat("❌ 保本执行失败 (票号: %d) 错误: %d", ticket, errorCode));
        }
    }

    return false;
}

int OnInit()
{

    // 初始化高质量随机种子
    ulong seed = GetMicrosecondCount() ^
                 ((ulong)GetTickCount() << 32) ^
                 ((ulong)_Symbol[0] << 16) ^
                 ((ulong)MagicNumber << 8) ^
                 ((ulong)TimeCurrent());
    MathSrand((int)(seed & 0x7FFFFFFF)); 

    // 初始化品种参数
    symCache.point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    symCache.digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    symCache.minLots = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    symCache.maxLots = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    symCache.lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    symCache.lotDigits = (symCache.lotStep == 0.01) ? 2 :
                         (symCache.lotStep == 0.1) ? 1 : 0;

    // 初始化手数格式化缓存变量
    g_minVolume = symCache.minLots;
    g_maxVolume = symCache.maxLots;
    g_stepVolume = symCache.lotStep;
    g_volumeDigits = symCache.lotDigits;

    // 解析交易时间
    StringToTimeCache(StartTime, EndTime, timeCache);

    // 配置交易对象
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(5); // 合理滑点
    trade.SetTypeFillingBySymbol(_Symbol); // 默认填充类型
    trade.LogLevel(LOG_LEVEL_ERRORS);

    orderComment = OrderComment;

    // 输出保本检查设置信息
    if(CheckOrdersCount <= 0) {
        Print("保本检查功能已禁用(CheckOrdersCount=0)，允许无限开仓");
    } else {
        //Print("保本检查功能已启用，检查最新", CheckOrdersCount, "个订单，每个已移动保本止损的订单允许开1个新仓");
    }

    // 创建信息面板背景
    if(!ObjectCreate(0, INFO_PANEL_BG, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        Print("创建信息面板背景失败!");
        return INIT_FAILED;
    }
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_XDISTANCE, 0);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_YDISTANCE, 18);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_XSIZE, 250);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_YSIZE, 380); // 增加高度以容纳历史盈亏和总盈亏标签
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_BGCOLOR, clrMidnightBlue);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_BACK, false);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_SELECTED, false);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_HIDDEN, true);
    ObjectSetInteger(0, INFO_PANEL_BG, OBJPROP_ZORDER, 0);

    // 创建左上角UI - 杠杆标签
    ObjectDelete(0, LABEL_LEVERAGE); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_LEVERAGE, OBJ_LABEL, 0, 0, 0))
    {
        Print("创建杠杆标签失败!");
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_YDISTANCE, 30);  // 调整位置
    ObjectSetString(0, LABEL_LEVERAGE, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_BGCOLOR, clrMidnightBlue);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_LEVERAGE, OBJPROP_SELECTABLE, false);

    // 创建左上角UI - 预付款维持率标签
    ObjectDelete(0, LABEL_MARGIN_LEVEL); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_MARGIN_LEVEL, OBJ_LABEL, 0, 0, 0))
    {
        Print("创建预付款维持率标签失败!");
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_YDISTANCE, 70);  // 调整位置
    ObjectSetString(0, LABEL_MARGIN_LEVEL, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_BGCOLOR, clrMidnightBlue);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_SELECTABLE, false);

    // 创建左上角UI - 余额标签
    ObjectDelete(0, LABEL_BALANCE); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_BALANCE, OBJ_LABEL, 0, 0, 0))
    {
        Print("创建余额标签失败!");
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_YDISTANCE, 110);  // 调整位置
    ObjectSetString(0, LABEL_BALANCE, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_BGCOLOR, clrMidnightBlue);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_BALANCE, OBJPROP_SELECTABLE, false);

    // 创建左上角UI - 净值标签
    ObjectDelete(0, LABEL_EQUITY); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_EQUITY, OBJ_LABEL, 0, 0, 0))
    {
        Print("创建净值标签失败!");
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_YDISTANCE, 150);  // 调整位置
    ObjectSetString(0, LABEL_EQUITY, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_COLOR, clrYellow);  // 改为黄色
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_BGCOLOR, clrDarkBlue);  // 使用更深的背景色
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_SELECTABLE, false);

    // 创建左上角UI - 多单手数标签
    ObjectDelete(0, LABEL_BUY_VOLUME); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_BUY_VOLUME, OBJ_LABEL, 0, 0, 0))
    {
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_YDISTANCE, 190);  // 调整位置
    ObjectSetString(0, LABEL_BUY_VOLUME, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_COLOR, clrLime);  // 多单用绿色
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_BGCOLOR, clrNONE);
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_BUY_VOLUME, OBJPROP_SELECTABLE, false);

    // 创建左上角UI - 空单手数标签
    ObjectDelete(0, LABEL_SELL_VOLUME); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_SELL_VOLUME, OBJ_LABEL, 0, 0, 0))
    {
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_YDISTANCE, 230);  // 调整位置
    ObjectSetString(0, LABEL_SELL_VOLUME, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_COLOR, clrRed);  // 空单用红色
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_BGCOLOR, clrNONE);
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_SELL_VOLUME, OBJPROP_SELECTABLE, false);

    // 极简版UI - 历史盈亏标签
    ObjectDelete(0, LABEL_HISTORY_PL); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_HISTORY_PL, OBJ_LABEL, 0, 0, 0))
    {
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_YDISTANCE, 270);
    ObjectSetString(0, LABEL_HISTORY_PL, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_BGCOLOR, clrNONE);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_SELECTABLE, false);
    // 不在这里设置初始文本，让UpdateProfitInfo来处理

    // 极简版UI - 总盈亏标签
    ObjectDelete(0, LABEL_TOTAL_PL); // 先删除可能存在的对象
    if(!ObjectCreate(0, LABEL_TOTAL_PL, OBJ_LABEL, 0, 0, 0))
    {
        return INIT_FAILED;
    }
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_YDISTANCE, 310);
    ObjectSetString(0, LABEL_TOTAL_PL, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_FONTSIZE, 18);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_BGCOLOR, clrNONE);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_BACK, false);
    ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_SELECTABLE, false);
    // 不在这里设置初始文本，让UpdateProfitInfo来处理

    // 创建上次平仓结余编辑框
    if(!ObjectCreate(0, BALANCE_EDIT, OBJ_EDIT, 0, 0, 0))
    {
        return INIT_FAILED;
    }
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_YDISTANCE, 350);  // 调整位置，在总盈亏标签下方
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_XSIZE, 200);      // 设置宽度
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_YSIZE, 30);       // 设置高度
    ObjectSetString(0, BALANCE_EDIT, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_FONTSIZE, 13);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_COLOR, clrBlack);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_BGCOLOR, clrWhite);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_BORDER_COLOR, clrGray);
    ObjectSetInteger(0, BALANCE_EDIT, OBJPROP_ALIGN, ALIGN_CENTER);
    ObjectSetString(0, BALANCE_EDIT, OBJPROP_TEXT, "上轮结余");

    // 尝试从全局变量中读取上次保存的结余值
    string globalVarName = Symbol() + "_LastClosingBalance_" + IntegerToString(MagicNumber);
    if(GlobalVariableCheck(globalVarName)) {
        lastClosingBalance = GlobalVariableGet(globalVarName);
        ObjectSetString(0, BALANCE_EDIT, OBJPROP_TEXT, "上轮结余: " + DoubleToString(lastClosingBalance, 2));
    }

    // 创建画水平线按钮
    if(!ObjectCreate(0, HLINE_BUTTON, OBJ_BUTTON, 0, 0, 0))
    {
       return INIT_FAILED;
    }
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_XDISTANCE, 122);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_YDISTANCE, 180);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_XSIZE, 100);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_YSIZE, 30);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_BGCOLOR, clrDarkSlateGray);
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_COLOR, clrWhite);
    ObjectSetString(0, HLINE_BUTTON, OBJPROP_TEXT, "画水平线");
    ObjectSetString(0, HLINE_BUTTON, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_FONTSIZE, 16);

    // 创建水平线开仓按钮
    if(!ObjectCreate(0, HLINE_TRADE_BUTTON, OBJ_BUTTON, 0, 0, 0))
    {
       return INIT_FAILED;
    }
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_XDISTANCE, 122);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_YDISTANCE, 220);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_XSIZE, 100);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_YSIZE, 30);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue);
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_COLOR, clrWhite);
    ObjectSetString(0, HLINE_TRADE_BUTTON, OBJPROP_TEXT, "画线开仓");
    ObjectSetString(0, HLINE_TRADE_BUTTON, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_FONTSIZE, 16);
    // 添加按钮状态属性，确保初始状态为弹起
    ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_STATE, false);

    // 创建随机开仓按钮
    if(!ObjectCreate(0, RANDOM_TRADE_BUTTON, OBJ_BUTTON, 0, 0, 0))
    {
       return INIT_FAILED;
    }
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_CORNER, CORNER_RIGHT_LOWER);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_XDISTANCE, 122);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_YDISTANCE, 260);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_XSIZE, 100);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_YSIZE, 30);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue);
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_COLOR, clrWhite);
    ObjectSetString(0, RANDOM_TRADE_BUTTON, OBJPROP_TEXT, "随机开仓");
    ObjectSetString(0, RANDOM_TRADE_BUTTON, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_FONTSIZE, 16);

    // 创建一键平仓按钮
    if(!ObjectCreate(0, CLOSE_BUTTON, OBJ_BUTTON, 0, 0, 0))
    {
        Print("创建按钮失败!");
        return INIT_FAILED;
    }
    // 设置按钮对齐方式为右下角
    ObjectSetInteger(0, "CloseAllButton", OBJPROP_CORNER, CORNER_RIGHT_LOWER);

    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_XDISTANCE, 122);
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_YDISTANCE, 100);
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_XSIZE, 100);
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_YSIZE, 30);
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_BGCOLOR, clrDarkGray);
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_COLOR, clrBlack);
    ObjectSetString(0, CLOSE_BUTTON, OBJPROP_TEXT, "一键平仓");
    ObjectSetString(0, CLOSE_BUTTON, OBJPROP_FONT, "Arial");
    ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_FONTSIZE, 16);

    // 添加按钮状态属性，确保初始状态为弹起
    ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_STATE, false);

    // 创建水平线（初始不显示）
    if(!ObjectCreate(0, RZ_LINE, OBJ_HLINE, 0, 0, 0))
    {
       return INIT_FAILED;
    }
    ObjectSetInteger(0, RZ_LINE, OBJPROP_COLOR, clrGold);
    ObjectSetInteger(0, RZ_LINE, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, RZ_LINE, OBJPROP_WIDTH, 2);
    ObjectSetString(0, RZ_LINE, OBJPROP_TEXT, "Rz_line");
    ObjectSetInteger(0, RZ_LINE, OBJPROP_BACK, true);
    ObjectSetInteger(0, RZ_LINE, OBJPROP_SELECTABLE, true);
    ObjectSetInteger(0, RZ_LINE, OBJPROP_HIDDEN, true); // 初始隐藏

    // 初始更新UI信息
    UpdateAccountInfo();

    // 初始计算盈亏信息
    UpdateProfitInfo();

    // 确保杠杆标签初始化正确
    double leverage = (double)AccountInfoInteger(ACCOUNT_LEVERAGE);
    ObjectSetString(0, LABEL_LEVERAGE, OBJPROP_TEXT, "杠杆: " + DoubleToString(leverage, 0) + ":1");

    // 更新持仓手数显示
    double totalLots = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol)
        {
            totalLots += PositionGetDouble(POSITION_VOLUME);
        }
    }
    ObjectSetString(0, "LABEL_POSITION_SIZE", OBJPROP_TEXT, StringFormat("持仓手数: %.2f", totalLots));

    // 设置定时器，每秒触发一次
    EventSetTimer(1);

    return INIT_SUCCEEDED;
}

//================================================工具函数================================================
//+------------------------------------------------------------------+
//| 时间参数转换函数                                                |
//+------------------------------------------------------------------+
void StringToTimeCache(const string start, const string end, TimeCache &cache)
{
    string t[2];
    // 处理开始时间
    if(StringSplit(start, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);

        // 确保时间在有效范围内
        hours = MathMin(MathMax(hours, 0), 23);
        minutes = MathMin(MathMax(minutes, 0), 59);

        cache.startMinutes = hours * 60 + minutes;
    }

    // 处理结束时间
    if(StringSplit(end, ':', t) == 2) {
        int hours = (int)StringToInteger(t[0]);
        int minutes = (int)StringToInteger(t[1]);

        // 特殊处理"24:00"，将其转换为"23:59"
        if(hours == 24) {
            hours = 23;
            minutes = 59;
        } else {
            // 确保时间在有效范围内
            hours = MathMin(MathMax(hours, 0), 23);
            minutes = MathMin(MathMax(minutes, 0), 59);
        }

        cache.endMinutes = hours * 60 + minutes;
    }

    // 只在EA初始化时打印一次
    static bool firstRun = true;
    if(firstRun) {
        Print("交易时间设置: 开始时间=", start, " (", cache.startMinutes, "分钟), 结束时间=", end, " (", cache.endMinutes, "分钟)");
        firstRun = false;
    }
}

//+------------------------------------------------------------------+
//| 交易时间验证函数                                                |
//+------------------------------------------------------------------+
bool IsTradeTime(const MqlDateTime &dt)
{
    // 预先计算当前分钟数，避免在函数内部重复计算
    static int lastHour = -1, lastMin = -1, cachedResult = -1;

    // 如果时间没变，直接返回缓存结果
    if(lastHour == dt.hour && lastMin == dt.min && cachedResult != -1)
        return cachedResult != 0;

    // 计算当前分钟数
    int current = dt.hour * 60 + dt.min;

    // 处理跨午夜的时间段
    bool result;
    if(timeCache.startMinutes < timeCache.endMinutes) {
        // 正常时间段（例如 09:00-17:00）
        result = (current >= timeCache.startMinutes && current <= timeCache.endMinutes);
    } else {
        // 跨午夜时间段（例如 22:00-06:00）
        result = (current >= timeCache.startMinutes || current <= timeCache.endMinutes);
    }

    // 更新缓存
    lastHour = dt.hour;
    lastMin = dt.min;
    cachedResult = result ? 1 : 0;

    // 记录上次结果用于OnTimer函数中的状态变化检测
    static bool lastResult = false;
    lastResult = result;

    return result;
}

//================================================核心逻辑================================================
//+------------------------------------------------------------------+
//| 价格跳动处理主函数                                              |
//+------------------------------------------------------------------+
void OnTick()
{
    // 使用静态变量减少函数调用
    static datetime lastInfoUpdate = 0;
    static datetime lastManageUpdate = 0;
    static datetime lastFullCheckUpdate = 0;
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    // 每秒更新一次账户信息和盈亏信息
    if(currentTime - lastInfoUpdate >= 1)
    {
        UpdateAccountInfo();
        UpdateProfitInfo();
        lastInfoUpdate = currentTime;
    }

    // 只在市场开放时执行订单管理
    if(IsMarketOpen())
    {
        // 每个tick都执行保本检查，确保及时性
        CheckBreakevenOnly();

        // 完整的订单管理使用较低频率（2秒）
        if(currentTime - lastFullCheckUpdate >= 2)
        {
            ManagePositions();
            lastFullCheckUpdate = currentTime;
        }
    }
}

//+------------------------------------------------------------------+
//| 优化的保本检查函数 - 立即触发保本机制                            |
//+------------------------------------------------------------------+
void CheckBreakevenOnly()
{
    int total = PositionsTotal();
    if(total == 0) return;

    // 获取当前市场报价
    MqlTick lastTick;
    if(!SymbolInfoTick(_Symbol, lastTick)) return;

    for(int i = 0; i < total; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(!PositionSelectByTicket(ticket)) continue;

        // 只处理当前品种和魔术号的订单
        if(PositionGetString(POSITION_SYMBOL) != _Symbol ||
           PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;

        // 检查是否已经修改过保本止损
        bool isModified = false;
        if(isModifiedMap.TryGetValue(ticket, isModified) && isModified) continue;

        // 获取订单信息
        ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
        double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
        double currentSL = PositionGetDouble(POSITION_SL);
        double currentPrice = (posType == POSITION_TYPE_BUY) ? lastTick.bid : lastTick.ask;

        // 计算保本激活点数
        double breakeven_activation_points = BreakevenPoints * _Point;

        // 检查是否达到保本条件
        bool reachedBreakeven = false;
        if(posType == POSITION_TYPE_BUY) {
            reachedBreakeven = (currentPrice - openPrice >= breakeven_activation_points);
        } else {
            reachedBreakeven = (openPrice - currentPrice >= breakeven_activation_points);
        }

        // 🚀 一旦达到保本条件，立即执行止损修改
        if(reachedBreakeven)
        {
            // 【严格保本】计算新的止损价格 - 精确使用用户设置的AdditionalPoints，绝不调整
            double newSL = (posType == POSITION_TYPE_BUY) ?
                openPrice + AdditionalPoints * _Point :
                openPrice - AdditionalPoints * _Point;

            // 标准化价格
            int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
            newSL = NormalizeDouble(newSL, digits);

            // 检查新止损是否与当前止损相同
            if(MathAbs(newSL - currentSL) < _Point/2)
            {
                // 如果止损值相同，只添加到breakEvenMap
                if(!ArrayContains(breakEvenMap, ticket))
                {
                    ArrayAppend(breakEvenMap, ticket);
                    isModifiedMap.Add(ticket, true);
                    //LogMessage(LOG_LEVEL_INFO, StringFormat("止损已经是目标值，仅添加到breakEvenMap (票号: %d)", ticket));
                }
                continue;
            }

            // 🚀 【严格保本】基于12.mq5思路的预验证机制
            double strictSL = CalculateStrictBreakeven(posType, openPrice, AdditionalPoints);

            // 预验证是否可以执行
            if (!CanExecuteBreakeven(strictSL, posType, currentPrice, currentSL)) {
                continue;
            }

            trade.SetExpertMagicNumber(MagicNumber);
            if(trade.PositionModify(ticket, strictSL, PositionGetDouble(POSITION_TP)))
            {
                // 更新状态
                isModifiedMap.Add(ticket, true);
                if(!ArrayContains(breakEvenMap, ticket))
                {
                    ArrayAppend(breakEvenMap, ticket);
                }
                LogMessage(LOG_LEVEL_INFO, StringFormat("✅ 【保本成功】票号: %d, 止损: %.5f (开仓价%s%d点)",
                    ticket, strictSL, (posType == POSITION_TYPE_BUY ? "+" : "-"), AdditionalPoints));
            }
            else
            {
                int errorCode = GetLastError();
                LogMessage(LOG_LEVEL_WARNING, StringFormat("❌ 修改保本止损失败 (票号: %d) 错误: %d", ticket, errorCode));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 【核心】基于12.mq5思路的预验证保本机制 - 严格按用户设置执行       |
//+------------------------------------------------------------------+
bool CanExecuteBreakeven(double targetSL, ENUM_POSITION_TYPE posType, double currentPrice, double currentSL)
{
    // 【关键1】基于Amazing.mq5的安全距离检查
    int stopsLevel = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    int freezeLevel = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL);

    // 检查新止损是否符合安全距离要求
    bool stopsCheck = false;
    if(posType == POSITION_TYPE_BUY) {
        stopsCheck = (currentPrice - targetSL > stopsLevel * _Point);
    } else {
        stopsCheck = (targetSL - currentPrice > stopsLevel * _Point);
    }

    // 【关键2】基于ClosePositionsModule的冻结区域检查
    bool freezeCheck = false;
    if(currentSL > 0) {
        freezeCheck = (MathAbs(currentPrice - currentSL) > freezeLevel * _Point);
    } else {
        freezeCheck = true; // 如果没有当前止损，则不受冻结限制
    }

    return (stopsCheck && freezeCheck);
}

//+------------------------------------------------------------------+
//| 【严格】计算保本止损 - 完全按照用户AdditionalPoints设置          |
//+------------------------------------------------------------------+
double CalculateStrictBreakeven(ENUM_POSITION_TYPE posType, double openPrice, int additionalPoints)
{
    // 【严格执行】完全按照用户设置计算，不做任何调整
    double targetSL;

    if(posType == POSITION_TYPE_BUY) {
        targetSL = openPrice + additionalPoints * _Point;
    } else {
        targetSL = openPrice - additionalPoints * _Point;
    }

    // 标准化价格（基于MACD Sample的做法）
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    return NormalizeDouble(targetSL, digits);
}

//+------------------------------------------------------------------+
//| 【时间戳随机器】高质量随机方向生成器 - 适配动态交易频率            |
//| 特色：基于时间戳的真随机性 + 零性能开销 + 完美适配频率变化         |
//+------------------------------------------------------------------+
ENUM_ORDER_TYPE GenerateRandomDirection()
{
    static int consecutiveCount = 0;          // 连续同方向计数器
    static ENUM_ORDER_TYPE lastDirection = ORDER_TYPE_BUY;  // 最后开仓方向
    
    // 时间戳随机器 - 使用微秒级时间戳的最后几位数字
    ulong timestamp = GetMicrosecondCount();
    
    // 提取时间戳的最后3位数字进行随机判断
    int randomValue = (int)(timestamp % 1000);
    
    // 生成基本随机方向（50/50概率）
    ENUM_ORDER_TYPE direction = (randomValue % 2 == 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    
    // 连续控制逻辑（保持原有的风险控制机制）
    if(direction == lastDirection) {
        consecutiveCount++;  // 增加连续计数
        
        // 连续4次同方向后强制反转
        if(consecutiveCount > 4) {
            direction = (direction == ORDER_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
            consecutiveCount = 1;  // 重置计数器
            LogMessage(LOG_LEVEL_INFO, StringFormat("🔄 连续控制: 强制切换到%s (避免连续5单)", 
                      (direction == ORDER_TYPE_BUY ? "多单" : "空单")));
        }
    } else {
        consecutiveCount = 1;  // 方向变化时重置计数器
    }
    
    lastDirection = direction;  // 更新最后方向
    
    // 调试统计（可选）
    static int totalCount = 0;
    static int buyCount = 0;
    totalCount++;
    if(direction == ORDER_TYPE_BUY) buyCount++;
    

    
    return direction;
}

//+------------------------------------------------------------------+
//| 随机开仓函数                                                     |
//+------------------------------------------------------------------+
bool OpenRandomPosition()
{
    // 1. 获取市场数据
    MqlTick last_tick;
    if(!SymbolInfoTick(_Symbol, last_tick)) {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("获取市场报价失败, 错误代码=%d", GetLastError()));
        return false;
    }

    // 2. 生成交易方向 - 使用改进的简单随机算法
    ENUM_ORDER_TYPE orderType = GenerateRandomDirection();

    // 3. 计算交易参数（类似您的简洁版本）
    double lots = NormalizeVolume(InitialLots);

    // 4. 发送交易指令（简化版本）
    bool result = false;

    // 确保交易对象设置正确
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(20);
    trade.SetTypeFillingBySymbol(_Symbol);
    trade.SetAsyncMode(false);

    if(orderType == ORDER_TYPE_BUY) {
        result = trade.Buy(lots, _Symbol, last_tick.ask,
                          CalculateSL(last_tick.ask, orderType),
                          CalculateTP(last_tick.ask, orderType),
                          orderComment);
    } else {
        result = trade.Sell(lots, _Symbol, last_tick.bid,
                           CalculateSL(last_tick.bid, orderType),
                           CalculateTP(last_tick.bid, orderType),
                           orderComment);
    }

    // 5. 记录结果
    uint resultCode = trade.ResultRetcode();
    if(result && resultCode == TRADE_RETCODE_DONE) {
        lastOrderTime = TimeCurrent();
        // 🚀 【Deepseek建议】仅在开仓成功后确认随机方向状态
        LogMessage(LOG_LEVEL_INFO, StringFormat("✅ 随机开仓成功: %s, 票号=%d, 手数=%.2f",
                  (orderType == ORDER_TYPE_BUY) ? "多单" : "空单", trade.ResultOrder(), lots));
    } else {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("❌ 随机开仓失败: %s, 错误=%d (状态未更新)",
                  (orderType == ORDER_TYPE_BUY) ? "多单" : "空单", resultCode));
    }

    return result;
}

//+------------------------------------------------------------------+
//| 水平线开仓函数                                                   |
//+------------------------------------------------------------------+
bool OpenHLinePosition()
{
    // 检查水平线是否存在且可见
    if(!ObjectGetInteger(0, RZ_LINE, OBJPROP_HIDDEN))
    {
        // 1. 获取水平线价格和市场数据
        double linePrice = ObjectGetDouble(0, RZ_LINE, OBJPROP_PRICE, 0);
        MqlTick tick;
        if(!SymbolInfoTick(_Symbol, tick)) {
            LogMessage(LOG_LEVEL_ERROR, StringFormat("获取市场报价失败, 错误代码=%d", GetLastError()));
            return false;
        }

        // 2. 确定交易方向
        ENUM_ORDER_TYPE type;
        if(tick.ask > linePrice) {
            type = ORDER_TYPE_BUY;
        } else if(tick.bid < linePrice) {
            type = ORDER_TYPE_SELL;
        } else {
            LogMessage(LOG_LEVEL_INFO, "当前价格在水平线附近，不执行交易");
            return false;
        }

        // 3. 计算交易参数
        double lots = NormalizeVolume(InitialLots);
        double price = (type == ORDER_TYPE_BUY) ? tick.ask : tick.bid;
        double sl = CalculateSL(price, type);
        double tp = CalculateTP(price, type);

        // 交易前验证
        if(!ValidateBeforeOrder(type, sl, tp)) {
            LogMessage(LOG_LEVEL_WARNING, StringFormat("%s交易验证失败", (type == ORDER_TYPE_BUY) ? "买入" : "卖出"));
            return false;
        }

        // 4. 发送交易指令
        bool result = false;
        string orderTypeStr = (type == ORDER_TYPE_BUY) ? "多单" : "空单";

        // 确保交易对象设置正确
        trade.SetExpertMagicNumber(MagicNumber);
        trade.SetDeviationInPoints(20);
        trade.SetTypeFillingBySymbol(_Symbol);
        trade.SetAsyncMode(false);

        if(type == ORDER_TYPE_BUY) {
            result = trade.Buy(lots, _Symbol, price, sl, tp, orderComment);
        } else {
            result = trade.Sell(lots, _Symbol, price, sl, tp, orderComment);
        }

        uint resultCode = trade.ResultRetcode();
        if(result && resultCode == TRADE_RETCODE_DONE) {
            lastOrderTime = TimeCurrent();
            LogMessage(LOG_LEVEL_INFO, StringFormat("水平线开仓成功: 类型=%s, 票号=%d, 手数=%.2f, 价格=%.5f, 止损=%.5f, 止盈=%.5f",
                      orderTypeStr, trade.ResultOrder(), lots, price, sl, tp));
        } else {
            LogMessage(LOG_LEVEL_ERROR, StringFormat("水平线开仓失败: 类型=%s, 错误代码=%d, 描述=%s",
                      orderTypeStr, resultCode, trade.ResultRetcodeDescription()));
        }

        return result;
    }
    else
    {
        LogMessage(LOG_LEVEL_INFO, "请先画水平线");
        return false;
    }
}

//+------------------------------------------------------------------+
//| 检查市场是否开放                                                 |
//+------------------------------------------------------------------+
bool IsMarketOpen()
{
    // 检查交易允许状态
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
        return false;

    // 检查连接状态
    if(!TerminalInfoInteger(TERMINAL_CONNECTED))
        return false;

    // 获取当前交易品种的交易状态
    ENUM_SYMBOL_TRADE_MODE tradeMode = (ENUM_SYMBOL_TRADE_MODE)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE);

    // 只有在完全交易模式下才认为市场开放
    return (tradeMode == SYMBOL_TRADE_MODE_FULL);
}

//+------------------------------------------------------------------+
//| 持仓管理函数                                                     |
//+------------------------------------------------------------------+
void ManagePositions()
{
    // 定期清理breakEvenMap，移除无效票号
    CleanupBreakEvenMap();

    int total = PositionsTotal();
    if(total == 0) {
        // 没有持仓时清空breakEvenMap
        ArrayFree(breakEvenMap);
        return;
    }

    // 使用改进的保本机制
    for(int i = 0; i < total; i++) {
        ulong ticket = PositionGetTicket(i);
        if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
           PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            CheckBreakeven(ticket);
        }
    }
}

//+------------------------------------------------------------------+
//| 保本条件检查函数                                                 |
//+------------------------------------------------------------------+
bool CheckBreakevenCondition(ulong ticket)
{
    // 初始检查
    if(!PositionSelectByTicket(ticket)) {
        RemoveTicketFromBreakEvenMap(ticket);
        //LogMessage(LOG_LEVEL_WARNING, StringFormat("CheckBreakevenCondition: 持仓不存在 (票号: %d)，从breakEvenMap中移除", ticket));
        return false;
    }

    // 检查是否为当前图表品种和魔术号
    string symbol = PositionGetString(POSITION_SYMBOL);
    long magic = PositionGetInteger(POSITION_MAGIC);

    if(symbol != _Symbol || magic != MagicNumber) return false;

    // 获取持仓信息
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);

    // 获取最新市场报价
    MqlTick lastTick;
    if(!SymbolInfoTick(symbol, lastTick)) {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("获取最新价格失败, 错误代码=%d", GetLastError()));
        return false;
    }

    // 根据持仓类型选择合适的价格
    double currentPrice = (posType == POSITION_TYPE_BUY) ? lastTick.bid : lastTick.ask;

    // 计算盈利点数
    double profit = MathAbs(currentPrice - openPrice);

    // 简化的保本条件：当前盈利点数 >= 保本点数
    bool result = (profit >= BreakevenPoints * symCache.point);

    return result;
}

//+------------------------------------------------------------------+
//| 修改订单止盈止损函数（包含保本和附加盈利点数功能）                 |
//+------------------------------------------------------------------+
bool ModifyOrderWithProtection(ulong ticket, double breakEvenAtPips=0, double addProfitPips=0)
{
    // 最大重试次数和延迟设置
    const int maxRetries = 3;        // 最多重试3次
    const double retryDelaySec = 1.0; // 每次间隔1秒
    const int minModifyInterval = 10; // 最小修改间隔(秒) - 增加到10秒
    const double minPriceChange = 0.5; // 最小价格变化(点) - 增加到0.5点

    // 使用静态映射记录每个订单的状态
    static CHashMap<ulong, datetime> lastModifyTimeMap;  // 上次修改时间

    datetime currentTime = TimeCurrent();
    datetime lastModTime = 0;
    bool isModified = false;

    // 获取订单状态
    if(!lastModifyTimeMap.TryGetValue(ticket, lastModTime)) {
        lastModTime = 0;
    }
    if(!isModifiedMap.TryGetValue(ticket, isModified)) {
        isModified = false;
    }

    // 如果订单已经修改过保本止损，直接返回
    if(isModified && ArrayContains(breakEvenMap, ticket)) {
        return false;
    }

    // 检查修改间隔
    if(lastModTime > 0 && currentTime - lastModTime < minModifyInterval) {
        return false;
    }

    // 检查市场是否开放，如果关闭则不执行修改
    if(!IsMarketOpen())
    {
        return false;
    }

    // 获取或初始化该订单的重试计数器
    int currentRetry = 0;
    if(!orderRetryMap.TryGetValue(ticket, currentRetry)) {
        currentRetry = 0;
    }

    // 1. 增加更严格的票号验证
    if(ticket == 0) {
        return false;
    }

    // 2. 增加多重持仓检查
    bool positionExists = false;
    for(int retry = 0; retry < 3; retry++) {
        if(PositionSelectByTicket(ticket)) {
            // 验证持仓状态
            long positionStatus = 0;
            if(PositionGetInteger(POSITION_TICKET, positionStatus) &&
               positionStatus == ticket &&
               PositionGetDouble(POSITION_VOLUME) > 0) {
                positionExists = true;
                break;
            }
        }
        Sleep(20); // 短暂等待后重试
    }

    if(!positionExists) {
        RemoveTicketFromBreakEvenMap(ticket);
        orderRetryMap.Remove(ticket);
        isModifiedMap.Remove(ticket); // 清理已修改记录
        return false;
    }

    // 获取持仓信息
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);

    // 获取当前市场报价
    MqlTick lastTick;
    if(!SymbolInfoTick(_Symbol, lastTick))
    {
        LogMessage(LOG_LEVEL_ERROR, StringFormat("获取市场报价失败 (重试: %d/%d)", (int)currentRetry, maxRetries));
        return false;
    }

    double currentPrice = (posType == POSITION_TYPE_BUY) ? lastTick.bid : lastTick.ask;

    // 【简化】直接使用经纪商限制，不需要额外的安全距离计算
    double minDistance = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    double freezeLevel = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_FREEZE_LEVEL) * _Point;

    // 使用较大的值作为最小距离
    minDistance = MathMax(minDistance, freezeLevel);

    // 【严格保本】计算新的止损价格 - 精确使用用户设置的AdditionalPoints，绝不调整
    double newSL = currentSL;
    if(breakEvenAtPips > 0)
    {
        // 严格保本逻辑：精确按照用户设置的AdditionalPoints执行
        newSL = (posType == POSITION_TYPE_BUY)
              ? openPrice + addProfitPips * _Point
              : openPrice - addProfitPips * _Point;
    }

    // 检查价格变化是否足够大 - 使用更严格的检查
    if(MathAbs(newSL - currentSL) < minPriceChange * _Point) {
        // 如果价格变化很小但仍需要记录保本状态
        if(breakEvenAtPips > 0 && !ArrayContains(breakEvenMap, ticket)) {
            ArrayAppend(breakEvenMap, ticket);
            isModifiedMap.Add(ticket, true); // 标记为已修改
        }
        return false;
    }

    // 执行订单修改
    trade.SetExpertMagicNumber(MagicNumber);
    int symbolDigits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);

    // 添加重试机制
    bool modifySuccess = false;
    for(int retry = 0; retry < 3; retry++)
    {
        if(!PositionSelectByTicket(ticket) || PositionGetDouble(POSITION_VOLUME) <= 0)
        {
            RemoveTicketFromBreakEvenMap(ticket);
            orderRetryMap.Remove(ticket);
            isModifiedMap.Remove(ticket); // 清理已修改记录
            return false;
        }

        if(trade.PositionModify(ticket, NormalizeDouble(newSL, symbolDigits), NormalizeDouble(currentTP, symbolDigits)))
        {
            modifySuccess = true;
            // 更新订单状态
            lastModifyTimeMap.Add(ticket, currentTime);
            isModifiedMap.Add(ticket, true); // 标记为已修改
            break;
        }

        int errorCode = GetLastError();

        // 特殊处理持仓已关闭的情况
        if(errorCode == 4756) // position closed
        {
            RemoveTicketFromBreakEvenMap(ticket);
            orderRetryMap.Remove(ticket);
            isModifiedMap.Remove(ticket); // 清理已修改记录
            return false;
        }

        // 【严格保本模式】特殊处理冻结错误 - 有限重试后记录失败
        if(errorCode == 130) // TRADE_RETCODE_FROZEN
        {
            if(retry < 2)
            {
                Sleep(500);
                continue;
            }
            else
            {
                LogMessage(LOG_LEVEL_WARNING, StringFormat("❌ 【严格保本失败】票号: %d, 订单持续冻结，严格保本模式拒绝调整", ticket));

                // 标记为已处理，避免重复尝试
                if(breakEvenAtPips > 0 && !ArrayContains(breakEvenMap, ticket)) {
                    ArrayAppend(breakEvenMap, ticket);
                    isModifiedMap.Add(ticket, true);
                }
                orderRetryMap.Remove(ticket);
                return false;
            }
        }

        // 【严格保本模式】处理无效止损错误 - 不调整用户设置，记录失败原因
        if(errorCode == 10016 || errorCode == 10017) // TRADE_RETCODE_INVALID_STOPS
        {
            LogMessage(LOG_LEVEL_WARNING, StringFormat("❌ 【严格保本失败】票号: %d, 错误: 无效止损距离 (错误码: %d)", ticket, errorCode));
            LogMessage(LOG_LEVEL_WARNING, StringFormat("用户设置的保本点数 %d 可能违反了经纪商的最小距离要求", (int)addProfitPips));
            LogMessage(LOG_LEVEL_WARNING, "严格保本模式: 拒绝调整用户设置，保本失败");

            // 仍然标记为已处理，避免重复尝试
            if(breakEvenAtPips > 0 && !ArrayContains(breakEvenMap, ticket)) {
                ArrayAppend(breakEvenMap, ticket);
                isModifiedMap.Add(ticket, true);
            }
            break; // 不再重试，严格按照用户设置
        }

        // 如果是临时性错误，等待后重试
        if(errorCode == 10011 || errorCode == 10012 || errorCode == 10013 || errorCode == 10014)
        {
            Sleep(300);
            continue;
        }

        break;
    }

    if(modifySuccess)
    {
        LogMessage(LOG_LEVEL_INFO, StringFormat("✅ 【保本成功】票号: %d, 新止损: %.5f (开仓价%s%d点，严格执行用户设置)",
                  ticket, newSL, (posType == POSITION_TYPE_BUY ? "+" : "-"), (int)addProfitPips));
        if(breakEvenAtPips > 0 && !ArrayContains(breakEvenMap, ticket))
        {
            ArrayAppend(breakEvenMap, ticket);
        }
        orderRetryMap.Remove(ticket);
        return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| 修改止损函数                                                     |
//+------------------------------------------------------------------+
void ModifyPositionSL(ulong ticket)
{
    // 检查票号是否有效
    if(ticket == 0) {
        LogMessage(LOG_LEVEL_WARNING, "ModifyPositionSL: 无效票号 (0)");
        return;
    }

    // 检查持仓是否存在 - 增加重试机制
    bool positionExists = false;
    for(int retry = 0; retry < 3; retry++) // 最多重试3次
    {
        if(PositionSelectByTicket(ticket))
        {
            positionExists = true;
            break;
        }
        Sleep(50); // 短暂等待后重试
    }

    if(!positionExists) {
        RemoveTicketFromBreakEvenMap(ticket);
        return;
    }

    // 检查是否为当前品种和魔术号
    string symbol = PositionGetString(POSITION_SYMBOL);
    long magic = PositionGetInteger(POSITION_MAGIC);
    if(symbol != _Symbol || magic != MagicNumber) {
        return;
    }

    // 使用ModifyOrderWithProtection函数替代原有逻辑
    ModifyOrderWithProtection(ticket, BreakevenPoints, AdditionalPoints);
}

//+------------------------------------------------------------------+
//| 异步平仓函数                                                     |
//+------------------------------------------------------------------+
bool ClosePositionsAsync()
{
    // 一次性设置所有交易参数
    trade.SetDeviationInPoints(INT_MAX);
    trade.SetAsyncMode(true);
    trade.SetMarginMode();
    trade.LogLevel(LOG_LEVEL_ERRORS);

    // 缓存当前品种，避免重复字符串比较
    string currentSymbol = _Symbol;

    for(uint retry = 0; retry < RTOTAL && !IsStopped(); retry++)
    {
        bool result = true;
        m_arr_tickets.Shutdown();

        // 预先获取持仓总数，避免在循环中重复调用
        int posTotal = PositionsTotal();

        // 第一次遍历：收集需要平仓的订单
        for(int i = 0; i < posTotal && !IsStopped(); i++)
        {
            if(m_position.SelectByIndex(i))
            {
                // 只处理当前图表品种和魔术号的订单
                if(m_position.Symbol() == currentSymbol && m_position.Magic() == MagicNumber)
                {
                    m_arr_tickets.Add(m_position.Ticket());
                }
            }
        }

        int ticketsTotal = m_arr_tickets.Total();
        if(ticketsTotal == 0)
        {
            //Print("当前品种 ", currentSymbol, " 没有持仓");
            return true;
        }

        //Print("找到当前品种 ", currentSymbol, " 持仓数: ", ticketsTotal);

        // 缓存常用值，避免重复获取
        int freeze_level = (int)SymbolInfoInteger(currentSymbol, SYMBOL_TRADE_FREEZE_LEVEL);
        double point = SymbolInfoDouble(currentSymbol, SYMBOL_POINT);
        double freeze_distance = freeze_level * point;

        // 第二次遍历：平仓操作
        for(int i = 0; i < ticketsTotal && !IsStopped(); i++)
        {
            ulong ticket = m_arr_tickets.At(i);
            if(m_position.SelectByTicket(ticket))
            {
                double priceCurrent = m_position.PriceCurrent();
                double takeProfit = m_position.TakeProfit();
                double stopLoss = m_position.StopLoss();

                bool TP_check = (MathAbs(priceCurrent - takeProfit) > freeze_distance);
                bool SL_check = (MathAbs(priceCurrent - stopLoss) > freeze_distance);

                if(TP_check && SL_check)
                {
                    trade.SetExpertMagicNumber(m_position.Magic());
                    trade.SetTypeFillingBySymbol(m_position.Symbol());
                    if(!trade.PositionClose(ticket) ||
                       (trade.ResultRetcode() != TRADE_RETCODE_DONE && trade.ResultRetcode() != TRADE_RETCODE_PLACED))
                    {
                        result = false;
                    }
                }
                else
                {
                    result = false;
                }
            }
        }

        if(result)
            break;

        Sleep(SLEEPTIME);
        PlaySound("timeout.wav");
    }
    return true;
}

//+------------------------------------------------------------------+
//| 按钮事件处理函数                                                 |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
    // 处理编辑框内容变化事件
    if(id == CHARTEVENT_OBJECT_ENDEDIT && sparam == BALANCE_EDIT)
    {
        string text = ObjectGetString(0, BALANCE_EDIT, OBJPROP_TEXT);
        // 尝试从文本中提取数字
        double balance = 0.0;

        // 如果文本包含"上轮结余:"，则提取后面的数字
        int pos = StringFind(text, ":");
        if(pos >= 0) {
            string balanceStr = StringSubstr(text, pos + 1);
            balance = StringToDouble(balanceStr);
        } else {
            // 否则尝试直接将整个文本转换为数字
            balance = StringToDouble(text);
        }

        // 如果成功提取到数字，则更新结余值
        if(balance > 0) {
            lastClosingBalance = balance;
            // 格式化显示
            ObjectSetString(0, BALANCE_EDIT, OBJPROP_TEXT, "上轮结余: " + DoubleToString(lastClosingBalance, 2));

            // 保存到全局变量
            string globalVarName = Symbol() + "_LastClosingBalance_" + IntegerToString(MagicNumber);
            GlobalVariableSet(globalVarName, lastClosingBalance);

            //Print("已保存上次平仓结余: ", DoubleToString(lastClosingBalance, 2));
        }
    }

    if(id == CHARTEVENT_OBJECT_CLICK)
   {
    if(sparam == CLOSE_BUTTON)
    {
        ClosePositionsAsync();
        // 设置按钮状态为弹起
        ObjectSetInteger(0, CLOSE_BUTTON, OBJPROP_STATE, false);
    }
    else if(sparam == HLINE_BUTTON)
    {
       // 获取图表的可视范围
       double chartHigh = ChartGetDouble(0, CHART_PRICE_MAX, 0); // 当前图表最高价
       double chartLow = ChartGetDouble(0, CHART_PRICE_MIN, 0);  // 当前图表最低价

       // 计算中间价格
       double midPrice = (chartHigh + chartLow) / 2;

       // 如果水平线已存在，则移动它，否则创建它
       if(!ObjectMove(0, RZ_LINE, 0, 0, midPrice))
       {
           Print("移动水平线失败!");
       }

       // 显示水平线并设置为选中状态，这样用户无需再次双击就能拖动
       ObjectSetInteger(0, RZ_LINE, OBJPROP_HIDDEN, false);
       ObjectSetInteger(0, RZ_LINE, OBJPROP_SELECTED, true);

       // 设置按钮状态为弹起
       ObjectSetInteger(0, HLINE_BUTTON, OBJPROP_STATE, false);
       ChartRedraw();
    }
    else if(sparam == HLINE_TRADE_BUTTON)
    {
       // 水平线开仓按钮点击事件
       // 获取按钮当前状态
       bool buttonState = ObjectGetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_STATE);

       if(buttonState) {
           // 按钮被按下
           isHLineTradeActive = true;            // 激活水平线开仓按钮
           isRandomTradeActive = false;          // 确保随机开仓按钮弹起
           ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_BGCOLOR, clrRed); // 设置为红色
           ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue); // 恢复随机按钮颜色
           ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_STATE, false); // 确保随机按钮弹起

           //Print("水平线开仓模式已激活，将由定时器检查条件并执行开仓");
       } else {
           // 按钮被弹起
           isHLineTradeActive = false;           // 关闭水平线开仓按钮
           ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue); // 恢复默认颜色
           //Print("水平线开仓模式已关闭");
       }
    }
    else if(sparam == RANDOM_TRADE_BUTTON)
    {
        // 随机开仓按钮点击事件
        // 获取按钮当前状态
        bool buttonState = ObjectGetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_STATE);

        if(buttonState) {
            // 按钮被按下
            isRandomTradeActive = true;           // 激活随机开仓按钮
            isHLineTradeActive = false;           // 确保水平线开仓按钮弹起
            ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_BGCOLOR, clrRed); // 设置为红色
            ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue); // 恢复水平线按钮颜色
            ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_STATE, false); // 确保水平线按钮弹起

            //Print("随机开仓模式已激活，将由定时器检查条件并执行开仓");
        } else {
            // 按钮被弹起
            isRandomTradeActive = false;          // 关闭随机开仓按钮
            ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_BGCOLOR, clrBlue); // 恢复默认颜色
            //Print("随机开仓模式已关闭");
        }
    }
  }
}

//+------------------------------------------------------------------+
//| 逆初始化函数                                                     |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 删除定时器
    EventKillTimer();

    // 清理全局数组和映射
    ArrayFree(breakEvenMap);
    orderRetryMap.Clear(); // 清理订单重试映射
    ObjectDelete(0, CLOSE_BUTTON);

    // 删除UI标签和背景
    ObjectDelete(0, LABEL_LEVERAGE);
    ObjectDelete(0, LABEL_MARGIN_LEVEL);
    ObjectDelete(0, LABEL_BALANCE);
    ObjectDelete(0, LABEL_EQUITY);
    ObjectDelete(0, LABEL_BUY_VOLUME);    // 删除多单手数标签
    ObjectDelete(0, LABEL_SELL_VOLUME);   // 删除空单手数标签
    ObjectDelete(0, LABEL_HISTORY_PL);    // 删除历史盈亏标签
    ObjectDelete(0, LABEL_TOTAL_PL);      // 删除总盈亏标签
    ObjectDelete(0, INFO_PANEL_BG);
    ObjectDelete(0, BALANCE_EDIT);        // 删除上次平仓结余编辑框

    // 删除水平线
    ObjectDelete(0, HLINE_BUTTON);
    ObjectDelete(0, HLINE_TRADE_BUTTON);
    ObjectDelete(0, RANDOM_TRADE_BUTTON);
    ObjectDelete(0, RZ_LINE);
}

//+------------------------------------------------------------------+
//| 止损计算函数                                                     |
//+------------------------------------------------------------------+
double CalculateSL(double price, ENUM_ORDER_TYPE type) {
    if(StopLoss <= 0) return 0.0; // 如果止损点数为0，则禁用止损

    double sl = 0;
    if(type == ORDER_TYPE_BUY) {
        sl = price - StopLoss * symCache.point;
    } else {
        sl = price + StopLoss * symCache.point;
    }

    // 确保止损距离符合经纪商要求
    int stops_level = (int)SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL);
    double min_distance = stops_level * symCache.point;

    // 对于BTC等高波动性品种，增加额外的安全边际，但不超过用户设置的止损点数
    if(StringFind(_Symbol, "BTC") >= 0 || StringFind(_Symbol, "btc") >= 0) {
        // 对于BTC，使用较大的安全距离，但不超过用户设置的止损点数
        min_distance = MathMax(min_distance, MathMin(100 * symCache.point, StopLoss * symCache.point * 0.1)); // 最多为用户设置止损的10%
    } else if(StringFind(_Symbol, "XAU") >= 0 || StringFind(_Symbol, "GOLD") >= 0) {
        // 对于黄金，使用适中的安全距离，但不超过用户设置的止损点数
        min_distance = MathMax(min_distance, MathMin(20 * symCache.point, StopLoss * symCache.point * 0.05)); // 最多为用户设置止损的5%
    } else {
        // 对于其他品种，使用较小的安全距离
        min_distance = MathMax(min_distance, MathMin(10 * symCache.point, StopLoss * symCache.point * 0.02)); // 最多为用户设置止损的2%
    }

    // 获取当前价格
    MqlTick tick;
    if(SymbolInfoTick(_Symbol, tick)) {
        double current_price = (type == ORDER_TYPE_BUY) ? tick.bid : tick.ask;
        double actual_distance = MathAbs(current_price - sl);

        // 如果止损距离小于最小要求，调整止损价格
        if(actual_distance < min_distance) {
            sl = (type == ORDER_TYPE_BUY) ?
                current_price - min_distance - 10 * symCache.point : // 额外增加10点安全距离
                current_price + min_distance + 10 * symCache.point;
        }


    }

    // 获取品种的精度信息
    int symbolDigits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    return NormalizeDouble(sl, symbolDigits);
}

//+------------------------------------------------------------------+
//| 止盈计算函数                                                     |
//+------------------------------------------------------------------+
double CalculateTP(double price, ENUM_ORDER_TYPE type)
{
    if(TakeProfit <= 0) return 0.0; // 如果止盈点数为0，则禁用止盈

    double tp = 0;
    if(type == ORDER_TYPE_BUY) {
        tp = price + TakeProfit * symCache.point;
    } else {
        tp = price - TakeProfit * symCache.point;
    }

    // 获取品种的精度信息
    int symbolDigits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    return NormalizeDouble(tp, symbolDigits);
}

//+------------------------------------------------------------------+
//| 数组操作函数                                                     |
//+------------------------------------------------------------------+
bool ArrayContains(const ulong &array[], ulong value)
{
    // 从后向前搜索，通常最新添加的元素在后面，可能会更快找到
    int size = ArraySize(array);
    for(int i = size-1; i >= 0; i--) {
        if(array[i] == value) return true;
    }
    return false;
}

void ArrayAppend(ulong &array[], ulong value)
{
    // 使用更高效的数组扩展策略
    int size = ArraySize(array);
    // 使用更大的预分配块以减少重新分配次数
    if(ArrayResize(array, size + 1, 100) > 0)
        array[size] = value;
}

//+------------------------------------------------------------------+
//| 标准化交易量 - 优化版                                            |
//+------------------------------------------------------------------+
double NormalizeVolume(double volume, double multiplier = 1.0)
{
    // 应用乘数
    volume *= multiplier;

    // 确保在最小和最大范围内
    volume = MathMax(g_minVolume, MathMin(g_maxVolume, volume));

    // 确保是步进的整数倍
    int steps = (int)MathRound(volume / g_stepVolume);
    volume = steps * g_stepVolume;

    // 格式化到正确的小数位
    return NormalizeDouble(volume, g_volumeDigits);
}

//+------------------------------------------------------------------+
//| 极简版盈亏统计 - 计算并更新盈亏信息（合并计算和显示）             |
//+------------------------------------------------------------------+
void UpdateProfitInfo()
{
   double history = 0, total = 0;

   // 计算历史盈亏
   HistorySelect(0, TimeCurrent());
   for(int i = HistoryDealsTotal()-1; i >= 0; i--)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(ticket > 0 &&
         HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         HistoryDealGetInteger(ticket, DEAL_MAGIC) == MagicNumber)
      {
         history += HistoryDealGetDouble(ticket, DEAL_PROFIT);
      }
   }

   // 计算当前持仓
   for(int i = PositionsTotal()-1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(ticket > 0 &&
         PositionGetString(POSITION_SYMBOL) == _Symbol &&
         PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         total += PositionGetDouble(POSITION_PROFIT);
      }
   }

   // 稳健的UI更新 - 确保对象存在且状态正确
   // 历史盈亏标签更新
   if(ObjectFind(0, LABEL_HISTORY_PL) >= 0)
   {
      string historyText = "历史: " + DoubleToString(history, 2);
      color historyColor = history >= 0 ? clrLime : clrRed;

      ObjectSetString(0, LABEL_HISTORY_PL, OBJPROP_TEXT, historyText);
      ObjectSetInteger(0, LABEL_HISTORY_PL, OBJPROP_COLOR, historyColor);
   }

   // 总盈亏标签更新
   if(ObjectFind(0, LABEL_TOTAL_PL) >= 0)
   {
      string totalText = "总盈亏: " + DoubleToString(history + total, 2);
      color totalColor = (history + total) >= 0 ? clrLime : clrRed;

      ObjectSetString(0, LABEL_TOTAL_PL, OBJPROP_TEXT, totalText);
      ObjectSetInteger(0, LABEL_TOTAL_PL, OBJPROP_COLOR, totalColor);
   }
}





//+------------------------------------------------------------------+
//| 更新账户信息                                                     |
//+------------------------------------------------------------------+
void UpdateAccountInfo()
{
    // 获取账户信息 - 一次性获取所有需要的数据
    double leverage = (double)AccountInfoInteger(ACCOUNT_LEVERAGE);
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double marginLevel = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);

    // 使用静态缓存减少字符串操作
    static string leverageStr = "";
    static double lastLeverage = 0;

    // 只在杠杆变化时更新
    if(leverage != lastLeverage) {
        leverageStr = "杠杆: " + DoubleToString(leverage, 0) + ":1";
        lastLeverage = leverage;
        ObjectSetString(0, LABEL_LEVERAGE, OBJPROP_TEXT, leverageStr);
    }

    // 更新预付款维持率标签
    static string marginLevelText;
    static color marginLevelColor;

    // 如果没有持仓，维持率显示为无穷大
    if(margin == 0 || marginLevel == 0)
    {
        marginLevelText = "维持率: ∞";
        marginLevelColor = clrLime;
    }
    else
    {
        marginLevelText = "维持率: " + DoubleToString(marginLevel, 2) + "%";

        // 根据维持率值设置不同颜色
        if(marginLevel >= 500)
            marginLevelColor = clrLime; // 安全水平，绿色
        else if(marginLevel >= 200)
            marginLevelColor = clrYellow; // 中等水平，黄色
        else
            marginLevelColor = clrRed; // 危险水平，红色
    }

    ObjectSetString(0, LABEL_MARGIN_LEVEL, OBJPROP_TEXT, marginLevelText);
    ObjectSetInteger(0, LABEL_MARGIN_LEVEL, OBJPROP_COLOR, marginLevelColor);

    // 更新余额标签 - 使用StringFormat代替字符串连接
    ObjectSetString(0, LABEL_BALANCE, OBJPROP_TEXT, StringFormat("余额: %.2f", balance));

    // 更新净值标签
    static string equityText;
    static color equityColor;

    equityText = StringFormat("► 净值: %.2f", equity);

    // 如果净值大于余额，显示为绿色；如果小于余额，显示为红色
    if(equity > balance)
        equityColor = clrLime; // 使用亮绿色表示盈利
    else if(equity < balance)
        equityColor = clrRed; // 使用红色表示亏损
    else
        equityColor = clrYellow; // 使用黄色表示持平

    ObjectSetString(0, LABEL_EQUITY, OBJPROP_TEXT, equityText);
    ObjectSetInteger(0, LABEL_EQUITY, OBJPROP_COLOR, equityColor);

    // 更新持仓手数标签 - 优化循环
    double totalLots = 0;
    double buyLots = 0;
    double sellLots = 0;
    double todayTotalLots = 0; // 当日活跃持仓总手数
    double todayHistoryLots = 0; // 当日已平仓总手数
    int posTotal = PositionsTotal();

    // 获取当前日期和当天开始时间
    MqlDateTime now;
    TimeToStruct(TimeCurrent(), now);
    string todayDate = StringFormat("%04d.%02d.%02d", now.year, now.mon, now.day);

    // 计算当天开始时间（00:00:00）
    datetime todayStart = StringToTime(StringFormat("%04d.%02d.%02d 00:00:00", now.year, now.mon, now.day));
    datetime currentTime = TimeCurrent();

    // 统计当前活跃持仓
    for(int i = 0; i < posTotal; i++)
    {
        string posSymbol = PositionGetSymbol(i);
        if(posSymbol == _Symbol)
        {
            double volume = PositionGetDouble(POSITION_VOLUME);
            totalLots += volume;

            // 区分多单和空单
            ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            if(posType == POSITION_TYPE_BUY)
                buyLots += volume;
            else if(posType == POSITION_TYPE_SELL)
                sellLots += volume;

            // 检查是否是当日开仓
            datetime posTime = (datetime)PositionGetInteger(POSITION_TIME);
            MqlDateTime posDateTime;
            TimeToStruct(posTime, posDateTime);
            string posDate = StringFormat("%04d.%02d.%02d", posDateTime.year, posDateTime.mon, posDateTime.day);

            // 如果是当日开仓，计入当日总手数
            if(posDate == todayDate)
                todayTotalLots += volume;
        }
    }

    // 统计当日历史订单（已平仓）
    if(HistorySelect(todayStart, currentTime))
    {
        int totalDeals = HistoryDealsTotal();

        for(int i = 0; i < totalDeals; i++)
        {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(dealTicket > 0)
            {
                // 检查是否是当前品种和魔术号
                string dealSymbol = HistoryDealGetString(dealTicket, DEAL_SYMBOL);
                long dealMagic = HistoryDealGetInteger(dealTicket, DEAL_MAGIC);

                if(dealSymbol == _Symbol && dealMagic == MagicNumber)
                {
                    // 只统计开仓交易，避免重复计算
                    ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(dealTicket, DEAL_ENTRY);
                    if(dealEntry == DEAL_ENTRY_IN) // 只统计开仓交易
                    {
                        double dealVolume = HistoryDealGetDouble(dealTicket, DEAL_VOLUME);
                        todayHistoryLots += dealVolume;
                    }
                }
            }
        }
    }

    // 计算当日总手数（当前持仓 + 已平仓）
    double totalTodayLots = todayTotalLots + todayHistoryLots;

    // 更新多单手数标签
    ObjectSetString(0, LABEL_BUY_VOLUME, OBJPROP_TEXT, StringFormat("多单: %.2f", buyLots));

    // 更新空单手数标签
    ObjectSetString(0, LABEL_SELL_VOLUME, OBJPROP_TEXT, StringFormat("空单: %.2f", sellLots));

    // 极简版盈亏更新已移至UpdateProfitInfo函数

    // 刷新图表 - 减少不必要的刷新
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| 检查最新订单是否已触发保本条件                                    |
//+------------------------------------------------------------------+
/**
 * 检查最新订单是否已触发保本条件，并决定是否允许开新仓
 *
 * 功能说明：
 * 1. 如果CheckOrdersCount参数为0或负数，则禁用保本检查，允许无限开仓
 * 2. 如果当前持仓数小于CheckOrdersCount，允许开仓直到达到CheckOrdersCount个
 * 3. 如果当前持仓数大于等于CheckOrdersCount，则检查最新的CheckOrdersCount个订单
 *    是否都已达到保本条件，只有达到保本条件的订单才允许开对应数量的新仓
 *
 * @return bool - 是否允许开新仓
 */
bool CheckLastOrdersBreakeven() {
    // 检查是否禁用保本检查功能
    if(CheckOrdersCount <= 0) {
        allowedNewPositions = INT_MAX;  // 设置为最大值，表示无限制
        return true;  // 允许无限开仓
    }

    // 获取当前品种和魔术号的持仓总数
    int total = GetPositionsCount();

    // 获取最新的N个订单
    CArrayLong latestTickets;
    bool getPositionsResult = GetLatestPositions(latestTickets, CheckOrdersCount);

    // 情况1: 当前持仓数小于CheckOrdersCount
    if(total < CheckOrdersCount) {
        // 允许开仓直到达到CheckOrdersCount个
        allowedNewPositions = CheckOrdersCount - total;

        // 完全移除日志输出，只记录上次允许开仓数量
        static int lastAllowedPositions = -1;
        lastAllowedPositions = allowedNewPositions;
        return true;  // 允许开仓
    }

    // 情况2: 无法获取最新订单
    if(latestTickets.Total() == 0) {
        allowedNewPositions = 0;  // 不允许开新仓

        // 完全移除日志输出
        return false;  // 不允许开仓
    }

    // 情况3: 检查最新的订单是否已达到保本条件
    int breakEvenCount = 0;  // 已达到保本条件的订单数量
    for(int i = 0; i < latestTickets.Total(); i++) {
        ulong ticket = latestTickets.At(i);
        // 验证票号是否有效
        if(ticket > 0 && CheckTicketBreakeven(ticket)) {  // 检查单个订单是否已达到保本条件
            breakEvenCount++;  // 计数已达到保本条件的订单
        }
    }

    // 允许开的新仓数量等于已达到保本条件的订单数量
    allowedNewPositions = breakEvenCount;
    bool result = (allowedNewPositions > 0);  // 是否允许开新仓

    // 完全移除日志输出，只记录上次状态
    static int lastAllowedPositions = -1;
    static bool lastResult = false;

    // 更新状态记录，不打印日志
    lastResult = result;
    lastAllowedPositions = allowedNewPositions;

    return result;  // 返回是否允许开新仓
}

//+------------------------------------------------------------------+
//| 从breakEvenMap中移除单个票号                                     |
//+------------------------------------------------------------------+
void RemoveTicketFromBreakEvenMap(ulong ticket)
{
    int size = ArraySize(breakEvenMap);
    for(int i = size-1; i >= 0; i--)
    {
        if(breakEvenMap[i] == ticket)
        {
            // 将元素移到数组末尾然后缩小数组
            for(int j = i; j < size-1; j++)
            {
                breakEvenMap[j] = breakEvenMap[j+1];
            }
            ArrayResize(breakEvenMap, size-1);

            break;
        }
    }
}

//+------------------------------------------------------------------+
//| 从breakEvenMap中移除单个票号 (旧方法，保留兼容性)                 |
//+------------------------------------------------------------------+
void RemoveFromBreakEvenMap(ulong ticket)
{
    // 调用新方法
    RemoveTicketFromBreakEvenMap(ticket);
}

//+------------------------------------------------------------------+
//| 检查订单是否已处理                                               |
//+------------------------------------------------------------------+
bool IsOrderProcessed(ulong ticket)
{
    for(int i = 0; i < ArraySize(processedTickets); i++) {
        if(processedTickets[i] == ticket) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| 标记订单为已处理                                                 |
//+------------------------------------------------------------------+
void MarkOrderAsProcessed(ulong ticket)
{
    if(!IsOrderProcessed(ticket)) {
        int size = ArraySize(processedTickets);
        ArrayResize(processedTickets, size + 1);
        processedTickets[size] = ticket;
    }
}

//+------------------------------------------------------------------+
//| 清理已处理订单                                                   |
//+------------------------------------------------------------------+
void CleanupProcessedOrders()
{
    int size = ArraySize(processedTickets);
    if(size == 0) return;

    // 创建临时数组存储有效的票号
    ulong validTickets[];
    int validCount = 0;

    // 检查每个订单是否仍然存在
    for(int i = 0; i < size; i++) {
        if(PositionSelectByTicket(processedTickets[i])) {
            ArrayResize(validTickets, validCount + 1);
            validTickets[validCount++] = processedTickets[i];
        }
    }

    // 更新已处理订单数组
    ArrayFree(processedTickets);
    ArrayCopy(processedTickets, validTickets);

    if(size != validCount) {
        //LogMessage(LOG_LEVEL_INFO, StringFormat("已清理过期订单，当前处理订单数量：%d", validCount));
    }
}

//+------------------------------------------------------------------+
//| 清理breakEvenMap数组，删除无效票号                               |
//+------------------------------------------------------------------+
void CleanupBreakEvenMap()
{
    int initialSize = ArraySize(breakEvenMap);
    if(initialSize == 0) return;

    ulong validTickets[];
    int validCount = 0;

    for(int i = 0; i < initialSize; i++)
    {
        ulong ticket = breakEvenMap[i];
        if(ticket == 0) continue;

        if(PositionSelectByTicket(ticket) &&
           PositionGetString(POSITION_SYMBOL) == _Symbol &&
           PositionGetInteger(POSITION_MAGIC) == MagicNumber)
        {
            ArrayResize(validTickets, validCount + 1);
            validTickets[validCount++] = ticket;
        }
    }

    ArrayFree(breakEvenMap);
    if(validCount > 0)
    {
        ArrayCopy(breakEvenMap, validTickets, 0, 0, validCount);
    }
}

//+------------------------------------------------------------------+
//| 清理无效订单的重试记录                                           |
//+------------------------------------------------------------------+
void CleanupInvalidTickets()
{
    // 1. 获取所有键值对
    ulong keys[];
    int values[];
    int count = orderRetryMap.CopyTo(keys, values);

    // 如果没有数据，直接返回
    if(count <= 0) {
        return;
    }

    // 记录已移除的票号数量
    int removedCount = 0;

    // 2. 检查无效票号
    for(int i = 0; i < count; i++)
    {
        if(!PositionSelectByTicket(keys[i]))
        {
            // 3. 记录需要移除的票号
            if(!orderRetryMap.Remove(keys[i]))
            {
                LogMessage(LOG_LEVEL_ERROR, StringFormat("无法移除票号 %d 的重试记录", keys[i]));
            }
            else
            {
                // 同时清理其他相关记录
                static CHashMap<ulong, datetime> lastModifyTimeMap;

                lastModifyTimeMap.Remove(keys[i]);
                isModifiedMap.Remove(keys[i]);

                removedCount++;
            }
        }
    }

    // 添加总结性日志
    if(removedCount > 0) {
        LogMessage(LOG_LEVEL_INFO, StringFormat("共清理了 %d 个无效订单的所有记录", removedCount));
    }
}

//+------------------------------------------------------------------+
//| 检查单个订单是否已达到保本条件                                    |
//+------------------------------------------------------------------+
bool CheckTicketBreakeven(ulong ticket)
{
    // 验证票号是否有效
    if(ticket == 0) {
        LogMessage(LOG_LEVEL_WARNING, "尝试检查无效票号 (0) 的保本状态");
        return false;
    }

    // 简化版：只检查订单是否在breakEvenMap数组中
    // 由于ModifyOrderWithProtection函数已经包含了保本条件检查和已处理订单记录的逻辑
    // 这个函数只需要检查订单是否已经被记录为达到保本条件
    bool result = ArrayContains(breakEvenMap, ticket);



    return result;
}

//+------------------------------------------------------------------+
//| 获取持仓数量                                                     |
//+------------------------------------------------------------------+
int GetPositionsCount()
{
    int count = 0;
    int posTotal = PositionsTotal();
    string currentSymbol = _Symbol; // 缓存当前品种
    long currentMagic = MagicNumber; // 缓存魔术号

    for(int i = 0; i < posTotal; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(ticket &&
           PositionGetString(POSITION_SYMBOL) == currentSymbol &&
           PositionGetInteger(POSITION_MAGIC) == currentMagic)
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| 获取最新的N个持仓                                                |
//+------------------------------------------------------------------+
bool GetLatestPositions(CArrayLong &tickets, int count)
{
    tickets.Clear();

    // 临时数组存储票号和时间
    ulong ticketsArray[];
    datetime timesArray[];
    int validCount = 0;

    // 预分配数组空间，减少重新分配
    int posTotal = PositionsTotal();
    ArrayResize(ticketsArray, posTotal);
    ArrayResize(timesArray, posTotal);

    // 收集所有符合条件的持仓
    string currentSymbol = _Symbol; // 缓存当前品种
    long currentMagic = MagicNumber; // 缓存魔术号

    for(int i = 0; i < posTotal; i++)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket) &&
           PositionGetString(POSITION_SYMBOL) == currentSymbol &&
           PositionGetInteger(POSITION_MAGIC) == currentMagic)
        {
            // 直接存储票号和时间，无需重新分配
            ticketsArray[validCount] = ticket;
            timesArray[validCount] = (datetime)PositionGetInteger(POSITION_TIME);
            validCount++;
        }
    }

    // 如果没有有效持仓，返回false
    if(validCount == 0) return false;

    // 使用冒泡排序，但优化实现
    for(int i = 0; i < validCount - 1; i++)
    {
        for(int j = 0; j < validCount - i - 1; j++)
        {
            if(timesArray[j] < timesArray[j+1])
            {
                // 交换时间和票号
                datetime tempTime = timesArray[j];
                timesArray[j] = timesArray[j+1];
                timesArray[j+1] = tempTime;

                ulong tempTicket = ticketsArray[j];
                ticketsArray[j] = ticketsArray[j+1];
                ticketsArray[j+1] = tempTicket;
            }
        }
    }

    // 将排序后的票号添加到CArrayLong中
    int numToAdd = MathMin(count, validCount);
    for(int i = 0; i < numToAdd; i++)
    {
        tickets.Add(ticketsArray[i]);
    }

    return tickets.Total() > 0;
}

//+------------------------------------------------------------------+
//| 交易事件处理函数                                                 |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 【高性能优化】交易事件处理 - 智能缓存刷新
    static datetime lastTradeUpdate = 0;
    datetime currentTime = TimeCurrent();

    // 防止过于频繁的更新（最少间隔1秒）
    if(currentTime - lastTradeUpdate >= 1)
    {
        // 立即更新账户信息和盈亏信息
        UpdateAccountInfo();
        UpdateProfitInfo();
        lastTradeUpdate = currentTime;
    }
}

//+------------------------------------------------------------------+
//| 优化的定时器处理                                                 |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 更新账户信息和盈亏信息
    UpdateAccountInfo();
    UpdateProfitInfo();

    static datetime lastCheckTime = 0;
    static datetime lastCleanupTime = 0;
    datetime currentTime = TimeCurrent();

    // 每5秒清理一次数组和映射
    if(currentTime - lastCleanupTime >= 5)
    {
        // 清理已处理但不再存在的订单
        CleanupProcessedOrders();
        CleanupBreakEvenMap();
        // 清理无效订单的重试记录
        CleanupInvalidTickets();
        lastCleanupTime = currentTime;
    }

    // 检查时间间隔控制 - 定时器触发间隔
    // 这里不应该使用TimeInterval，而应该使用一个较小的值，例如1秒
    if(currentTime - lastCheckTime < 1) return;

    // 交易时间控制
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    // 检测交易时间参数是否变化
    static string lastStartTime = StartTime;
    static string lastEndTime = EndTime;

    // 如果交易时间参数发生变化，重新解析时间设置
    if(lastStartTime != StartTime || lastEndTime != EndTime) {
        Print("交易时间设置已变更: 开始时间=", StartTime, ", 结束时间=", EndTime);
        StringToTimeCache(StartTime, EndTime, timeCache);
        lastStartTime = StartTime;
        lastEndTime = EndTime;
    }

    // 使用静态变量记录上次状态，只在状态变化时打印
    static bool lastTradeTimeStatus = true;
    bool isTradeTime = IsTradeTime(dt);

    // 只在状态从可交易变为不可交易时打印一次
    if(!isTradeTime && lastTradeTimeStatus != isTradeTime) {
        Print("交易时间状态变化: 当前不在交易时间内");
        lastTradeTimeStatus = isTradeTime;
    }
    // 只在状态从不可交易变为可交易时打印一次
    else if(isTradeTime && lastTradeTimeStatus != isTradeTime) {
        Print("交易时间状态变化: 当前在交易时间内");
        lastTradeTimeStatus = isTradeTime;
    }

    if(!isTradeTime) {
        lastCheckTime = currentTime;  // 更新检查时间
        return;
    }

    // 检查市场是否开放
    if(!IsMarketOpen())
    {
        // 市场关闭时不执行订单管理和开仓操作
        lastCheckTime = currentTime;  // 更新检查时间
        return;
    }

    // 按钮状态检查 - 同时检查按钮的物理状态和逻辑状态
    bool hlineButtonState = ObjectGetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_STATE);
    bool randomButtonState = ObjectGetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_STATE);

    // 确保按钮的物理状态和逻辑状态一致
    if(hlineButtonState != isHLineTradeActive) {
        isHLineTradeActive = hlineButtonState;
        ObjectSetInteger(0, HLINE_TRADE_BUTTON, OBJPROP_BGCOLOR, hlineButtonState ? clrRed : clrBlue);
        if(hlineButtonState) {
            //Print("水平线开仓模式已同步激活");
        } else {
            //Print("水平线开仓模式已同步关闭");
        }
    }

    if(randomButtonState != isRandomTradeActive) {
        isRandomTradeActive = randomButtonState;
        ObjectSetInteger(0, RANDOM_TRADE_BUTTON, OBJPROP_BGCOLOR, randomButtonState ? clrRed : clrBlue);
        if(randomButtonState) {
            //Print("随机开仓模式已同步激活");
        } else {
            //Print("随机开仓模式已同步关闭");
        }
    }

    // 如果两个按钮都没有激活，则不执行开仓
    if(!isHLineTradeActive && !isRandomTradeActive) {
        lastCheckTime = currentTime;  // 更新检查时间
        return;
    }

    // 保本检查
    if(!CheckLastOrdersBreakeven()) {
        lastCheckTime = currentTime;  // 更新检查时间
        return;
    }

    // 检查开单间隔 - 上次开仓后的时间间隔
    if(lastOrderTime > 0 && currentTime - lastOrderTime < TimeInterval) {
        // 简化调试信息
        lastCheckTime = currentTime;  // 更新检查时间
        return;
    }

    // 执行开仓
    bool tradeResult = false;

    // 检查是否允许开仓
    if(allowedNewPositions > 0) {
        // 执行开仓操作
        if(isHLineTradeActive) {
            tradeResult = OpenHLinePosition();
        } else if(isRandomTradeActive) {
            tradeResult = OpenRandomPosition();
        }

        // 如果开仓成功，更新最后交易时间和允许开仓数量
        if(tradeResult) {
            lastOrderTime = currentTime;  // 更新最后交易时间
            allowedNewPositions--;  // 减少允许开仓数量
        }
    }

    // 更新检查时间
    lastCheckTime = currentTime;  // 局部静态变量
}
//+------------------------------------------------------------------+
